#!/usr/bin/env python3
"""
Secure script to configure and test Gemini API key.
"""
import os
import sys
import json
import subprocess
import google.generativeai as genai
from dotenv import load_dotenv

def test_api_with_curl(api_key):
    """Test API key using curl command as specified in Gemini documentation."""
    print("🧪 Testing API key with curl command...")
    
    curl_command = [
        'curl',
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent',
        '-H', 'Content-Type: application/json',
        '-H', f'X-goog-api-key: {api_key}',
        '-X', 'POST',
        '-d', json.dumps({
            "contents": [
                {
                    "parts": [
                        {
                            "text": "Explain how AI works in a few words"
                        }
                    ]
                }
            ]
        })
    ]
    
    try:
        result = subprocess.run(curl_command, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            try:
                response_data = json.loads(result.stdout)
                if 'candidates' in response_data:
                    content = response_data['candidates'][0]['content']['parts'][0]['text']
                    print(f"✅ Curl test successful! Response: {content[:100]}...")
                    return True, content
                else:
                    print(f"❌ Unexpected response format: {result.stdout}")
                    return False, result.stdout
            except json.JSONDecodeError:
                print(f"❌ Invalid JSON response: {result.stdout}")
                return False, result.stdout
        else:
            print(f"❌ Curl command failed: {result.stderr}")
            return False, result.stderr
            
    except subprocess.TimeoutExpired:
        print("❌ Curl command timed out")
        return False, "Timeout"
    except Exception as e:
        print(f"❌ Error running curl: {e}")
        return False, str(e)

def test_api_with_python(api_key):
    """Test API key using Python google-generativeai library."""
    print("🐍 Testing API key with Python library...")

    try:
        genai.configure(api_key=api_key)
        # Use the current supported model instead of deprecated gemini-pro
        model = genai.GenerativeModel('gemini-2.0-flash')
        response = model.generate_content("Say hello in one word")

        if response and response.text:
            print(f"✅ Python test successful! Response: {response.text.strip()}")
            return True, response.text.strip()
        else:
            print("❌ No response from Python library")
            return False, "No response"

    except Exception as e:
        print(f"❌ Python test failed: {e}")
        return False, str(e)

def update_env_file(api_key):
    """Update the .env file with the new API key."""
    try:
        # Read current .env file
        with open('.env', 'r') as f:
            content = f.read()
        
        # Replace the API key line
        lines = content.split('\n')
        updated = False
        for i, line in enumerate(lines):
            if line.startswith('GEMINI_API_KEY='):
                lines[i] = f'GEMINI_API_KEY={api_key}'
                updated = True
                break
        
        if not updated:
            print("❌ Could not find GEMINI_API_KEY line in .env file")
            return False
        
        # Write back to file
        with open('.env', 'w') as f:
            f.write('\n'.join(lines))
        
        print("✅ .env file updated successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error updating .env file: {e}")
        return False

def test_bot_configuration():
    """Test that the bot can load and use the new API key."""
    print("🤖 Testing bot configuration...")
    
    try:
        # Reload environment variables
        load_dotenv(override=True)
        
        # Import and test bot components
        from config import Config
        from ai_processor import AIProcessor
        
        # Validate configuration
        Config.validate()
        print("✅ Configuration validation passed!")
        
        # Test AI processor initialization
        ai_processor = AIProcessor()
        print("✅ AI processor initialized successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ Bot configuration test failed: {e}")
        return False

def main():
    """Main configuration function."""
    print("🔧 Gemini API Key Configuration Tool")
    print("=" * 50)
    
    # Get API key from user
    print("\n📋 Please enter your Gemini API key:")
    print("   (The key should start with 'AIzaSy')")
    
    api_key = input("API Key: ").strip()
    
    if not api_key:
        print("❌ No API key provided. Exiting.")
        return False
    
    # Basic validation
    if not api_key.startswith('AIzaSy'):
        print("⚠️  Warning: API key doesn't start with 'AIzaSy'")
        print("   This might not be a valid Gemini API key.")
        continue_anyway = input("Continue anyway? (y/n): ").lower().strip()
        if continue_anyway != 'y':
            return False
    
    print(f"\n🔍 Testing API key: {api_key[:10]}...{api_key[-5:]}")
    
    # Test 1: Curl command (as per documentation)
    curl_success, curl_result = test_api_with_curl(api_key)
    
    # Test 2: Python library
    python_success, python_result = test_api_with_python(api_key)
    
    # Check if at least one test passed
    if not (curl_success or python_success):
        print("\n❌ Both tests failed. Please check your API key.")
        print("   Make sure:")
        print("   - The API key is correct")
        print("   - You have internet connectivity")
        print("   - The API key has proper permissions")
        return False
    
    if curl_success and python_success:
        print("\n🎉 All tests passed! API key is valid.")
    elif curl_success:
        print("\n✅ Curl test passed, Python test had issues (but API key is valid)")
    else:
        print("\n✅ Python test passed, curl test had issues (but API key is valid)")
    
    # Update .env file
    print("\n💾 Updating .env file...")
    if not update_env_file(api_key):
        return False
    
    # Test bot configuration
    if test_bot_configuration():
        print("\n🎉 Configuration complete! Your chatbot is ready to use.")
        print("\n🚀 Next steps:")
        print("   1. Run your bot: python3 run_bot.py")
        print("   2. Test it by messaging your bot on Telegram")
        return True
    else:
        print("\n⚠️  API key is valid but bot configuration needs attention.")
        print("   The .env file has been updated, but you may need to restart")
        print("   any running processes to pick up the new configuration.")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
