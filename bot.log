2025-07-30 01:57:57,497 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 01:57:57,497 - config - INFO - Starting Customer Support Bot...
2025-07-30 01:57:57,498 - conversation_memory - INFO - Loaded 2 conversations from conversations.json
2025-07-30 01:57:57,564 - config - <PERSON>F<PERSON> - <PERSON><PERSON> initialized successfully
2025-07-30 01:57:57,564 - config - INFO - <PERSON><PERSON> is running! Press Ctrl+C to stop.
2025-07-30 01:57:59,345 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 750, in _network_loop_retry
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 744, in do_action
    return action_cb_task.result()
           ~~~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 371, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 650, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 4480, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 619, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 354, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 648, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 202, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 385, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-30 01:58:03,579 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 750, in _network_loop_retry
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 744, in do_action
    return action_cb_task.result()
           ~~~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 371, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 650, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 4480, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 619, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 354, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 648, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 202, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 385, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-30 01:58:04,802 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 750, in _network_loop_retry
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 744, in do_action
    return action_cb_task.result()
           ~~~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 371, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 650, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 4480, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 619, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 354, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 648, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 202, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 385, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-30 01:58:14,384 - ai_processor - ERROR - Error generating AI greeting: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-30 01:58:20,975 - config - INFO - Processing message from user 1304611131: hi...
2025-07-30 01:58:21,276 - ai_processor - ERROR - Error analyzing intent: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-30 01:58:22,572 - ai_processor - ERROR - Error generating AI response: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-30 01:58:24,378 - config - INFO - Sent response to user 1304611131
2025-07-30 01:58:34,028 - __main__ - INFO - Received signal 2, shutting down...
2025-07-30 01:58:34,029 - config - INFO - Bot operation cancelled
2025-07-30 01:58:34,664 - config - ERROR - Error running bot: This Application is still running!
2025-07-30 01:58:34,667 - config - INFO - Bot shutdown complete
2025-07-30 01:58:34,667 - config - ERROR - Failed to start bot: This Application is still running!
2025-07-30 01:58:34,667 - asyncio - ERROR - unhandled exception during asyncio.run() shutdown
task: <Task finished name='Task-1' coro=<main() done, defined at /Users/<USER>/chatbot/telegram_bot.py:301> exception=RuntimeError('This Application is still running!')>
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py", line 712, in run_until_complete
    self.run_forever()
    ~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py", line 683, in run_forever
    self._run_once()
    ~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py", line 2004, in _run_once
    event_list = self._selector.select(timeout)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/selectors.py", line 548, in select
    kev_list = self._selector.control(None, max_ev, timeout)
  File "/Users/<USER>/chatbot/run_bot.py", line 16, in signal_handler
    sys.exit(0)
    ~~~~~~~~^^^
SystemExit: 0

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/chatbot/telegram_bot.py", line 309, in main
    await bot.run()
  File "/Users/<USER>/chatbot/telegram_bot.py", line 273, in run
    async with self.application:
               ^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_application.py", line 385, in __aexit__
    await self.shutdown()
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_application.py", line 535, in shutdown
    raise RuntimeError("This Application is still running!")
RuntimeError: This Application is still running!
2025-07-30 01:59:45,654 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 01:59:45,655 - config - INFO - Starting Customer Support Bot...
2025-07-30 01:59:45,656 - conversation_memory - INFO - Loaded 2 conversations from conversations.json
2025-07-30 01:59:45,725 - config - INFO - Bot initialized successfully
2025-07-30 01:59:45,725 - config - INFO - Bot is running! Press Ctrl+C to stop.
2025-07-30 01:59:53,624 - config - INFO - Processing message from user 1304611131: jj...
2025-07-30 01:59:57,164 - config - INFO - Sent response to user 1304611131
2025-07-30 02:00:05,545 - __main__ - INFO - Received signal 2, shutting down...
2025-07-30 02:00:05,546 - config - INFO - Bot operation cancelled
2025-07-30 02:00:09,254 - config - ERROR - Error running bot: This Application is still running!
2025-07-30 02:00:09,257 - config - INFO - Bot shutdown complete
2025-07-30 02:00:09,257 - config - ERROR - Failed to start bot: This Application is still running!
2025-07-30 02:00:09,257 - asyncio - ERROR - unhandled exception during asyncio.run() shutdown
task: <Task finished name='Task-1' coro=<main() done, defined at /Users/<USER>/chatbot/telegram_bot.py:301> exception=RuntimeError('This Application is still running!')>
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py", line 712, in run_until_complete
    self.run_forever()
    ~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py", line 683, in run_forever
    self._run_once()
    ~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py", line 2004, in _run_once
    event_list = self._selector.select(timeout)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/selectors.py", line 548, in select
    kev_list = self._selector.control(None, max_ev, timeout)
  File "/Users/<USER>/chatbot/run_bot.py", line 16, in signal_handler
    sys.exit(0)
    ~~~~~~~~^^^
SystemExit: 0

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/chatbot/telegram_bot.py", line 309, in main
    await bot.run()
  File "/Users/<USER>/chatbot/telegram_bot.py", line 273, in run
    async with self.application:
               ^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_application.py", line 385, in __aexit__
    await self.shutdown()
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_application.py", line 535, in shutdown
    raise RuntimeError("This Application is still running!")
RuntimeError: This Application is still running!
2025-07-30 02:02:24,869 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 02:02:24,870 - config - INFO - Starting Customer Support Bot...
2025-07-30 02:02:24,870 - conversation_memory - INFO - Loaded 2 conversations from conversations.json
2025-07-30 02:02:24,941 - config - INFO - Bot initialized successfully
2025-07-30 02:02:24,941 - config - INFO - Bot is running! Press Ctrl+C to stop.
2025-07-30 02:02:45,643 - __main__ - INFO - Received signal 2, shutting down...
2025-07-30 02:02:46,712 - config - INFO - Stopping bot polling...
2025-07-30 02:02:47,025 - config - INFO - Stopping bot application...
2025-07-30 02:02:47,031 - config - INFO - Bot shutdown complete
2025-07-30 02:02:59,088 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 02:02:59,088 - config - INFO - Starting Customer Support Bot...
2025-07-30 02:02:59,089 - conversation_memory - INFO - Loaded 2 conversations from conversations.json
2025-07-30 02:02:59,152 - config - INFO - Bot initialized successfully
2025-07-30 02:02:59,153 - config - INFO - Bot is running! Press Ctrl+C to stop.
2025-07-30 02:04:57,460 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 02:04:57,460 - config - INFO - Starting Customer Support Bot...
2025-07-30 02:04:57,461 - conversation_memory - INFO - Loaded 2 conversations from conversations.json
2025-07-30 02:04:57,532 - config - INFO - Bot initialized successfully
2025-07-30 02:04:57,532 - config - INFO - Bot is running! Press Ctrl+C to stop.
2025-07-30 02:05:09,475 - __main__ - INFO - Received shutdown signal, stopping bot...
2025-07-30 02:05:09,475 - config - INFO - Stopping bot polling...
2025-07-30 02:05:13,160 - config - INFO - Stopping bot application...
2025-07-30 02:05:13,164 - config - INFO - Bot shutdown complete
2025-07-30 02:05:13,165 - __main__ - INFO - Bot completed successfully
2025-07-30 02:05:26,121 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 02:05:26,122 - config - INFO - Starting Customer Support Bot...
2025-07-30 02:05:26,123 - conversation_memory - INFO - Loaded 2 conversations from conversations.json
2025-07-30 02:05:26,187 - config - INFO - Bot initialized successfully
2025-07-30 02:05:26,187 - config - INFO - Bot is running! Press Ctrl+C to stop.
2025-07-30 02:07:20,566 - config - INFO - Processing message from user 1304611131: jhgh...
2025-07-30 02:07:24,322 - config - INFO - Sent response to user 1304611131
2025-07-30 02:07:38,392 - config - INFO - Processing message from user 1304611131: who is india prime minsitor...
2025-07-30 02:07:43,330 - config - INFO - Sent response to user 1304611131
2025-07-30 02:09:08,878 - config - INFO - Processing message from user 1304611131: Current weather in jaipur...
2025-07-30 02:09:13,027 - config - INFO - Sent response to user 1304611131
2025-07-30 02:09:44,706 - config - INFO - Processing message from user 1304611131: You tell me whats the weather...
2025-07-30 02:09:48,654 - config - INFO - Sent response to user 1304611131
2025-07-30 02:09:55,756 - config - INFO - Processing message from user 1304611131: Yes...
2025-07-30 02:09:58,555 - config - INFO - Sent response to user 1304611131
2025-07-30 02:10:48,038 - config - INFO - Processing message from user 1304611131: You tell me current weather of jaipur...
2025-07-30 02:10:52,226 - config - INFO - Sent response to user 1304611131
2025-07-30 02:11:14,108 - config - INFO - Processing message from user 1304611131: How many cities in infia...
2025-07-30 02:11:17,001 - config - INFO - Sent response to user 1304611131
2025-07-30 02:11:17,001 - config - INFO - Processing message from user 1304611131: India...
2025-07-30 02:11:19,190 - config - INFO - Sent response to user 1304611131
2025-07-30 02:11:35,841 - config - INFO - Processing message from user 1304611131: Total country in the world...
2025-07-30 02:11:40,626 - config - INFO - Sent response to user 1304611131
2025-07-30 02:12:04,803 - config - INFO - Processing message from user 1304611131: Distance between india and USA...
2025-07-30 02:12:09,105 - config - INFO - Sent response to user 1304611131
2025-07-30 02:12:23,435 - config - INFO - Processing message from user 1304611131: frome jaipur...
2025-07-30 02:12:26,200 - config - INFO - Sent response to user 1304611131
2025-07-30 02:15:54,668 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 02:15:54,669 - config - INFO - Starting Customer Support Bot...
2025-07-30 02:15:54,670 - conversation_memory - INFO - Loaded 2 conversations from conversations.json
2025-07-30 02:15:54,735 - config - INFO - Bot initialized successfully
2025-07-30 02:15:54,735 - config - INFO - Bot is running! Press Ctrl+C to stop.
2025-07-30 02:15:56,250 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 750, in _network_loop_retry
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 744, in do_action
    return action_cb_task.result()
           ~~~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 371, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 650, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 4480, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 619, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 354, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 648, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 202, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 385, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-30 02:16:00,468 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 750, in _network_loop_retry
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 744, in do_action
    return action_cb_task.result()
           ~~~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 371, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 650, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 4480, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 619, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 354, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 648, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 202, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 385, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-30 02:16:01,701 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 750, in _network_loop_retry
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 744, in do_action
    return action_cb_task.result()
           ~~~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 371, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 650, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 4480, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 619, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 354, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 648, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 202, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 385, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-30 02:16:06,474 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 750, in _network_loop_retry
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 744, in do_action
    return action_cb_task.result()
           ~~~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 371, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 650, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 4480, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 619, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 354, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 648, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 202, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 385, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-30 02:16:08,185 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 750, in _network_loop_retry
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 744, in do_action
    return action_cb_task.result()
           ~~~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 371, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 650, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 4480, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 619, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 354, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 648, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 202, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 385, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-30 02:16:09,418 - __main__ - INFO - Received shutdown signal, stopping bot...
2025-07-30 02:16:09,418 - config - INFO - Stopping bot polling...
2025-07-30 02:16:13,647 - telegram.ext.Updater - ERROR - Error while calling `get_updates` one more time to mark all fetched updates as read: %s. Suppressing error to ensure graceful shutdown. When polling for updates is restarted, updates may be fetched again. Please adjust timeouts via `ApplicationBuilder` or the parameter `get_updates_request` of `Bot`.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 432, in _get_updates_cleanup
    await self.bot.get_updates(
    ...<8 lines>...
    )
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 650, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 4480, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 619, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 354, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 648, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 202, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 385, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-30 02:16:13,649 - config - INFO - Stopping bot application...
2025-07-30 02:16:13,651 - config - INFO - Bot shutdown complete
2025-07-30 02:16:13,651 - __main__ - INFO - Bot completed successfully
2025-07-30 02:19:33,582 - __main__ - INFO - Received shutdown signal, stopping bot...
2025-07-30 02:19:33,584 - config - INFO - Stopping bot polling...
2025-07-30 02:19:34,475 - config - INFO - Stopping bot application...
2025-07-30 02:19:34,481 - config - INFO - Bot shutdown complete
2025-07-30 02:19:34,481 - __main__ - INFO - Bot completed successfully
2025-07-30 02:19:42,743 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 02:19:42,744 - config - INFO - Starting Customer Support Bot...
2025-07-30 02:19:42,744 - conversation_memory - INFO - Loaded 2 conversations from conversations.json
2025-07-30 02:19:42,810 - config - INFO - Bot initialized successfully
2025-07-30 02:19:42,810 - config - INFO - Bot is running! Press Ctrl+C to stop.
2025-07-30 02:20:36,612 - config - INFO - Processing message from user 1304611131: Current weather in jaipur...
2025-07-30 02:20:43,330 - config - INFO - Sent response to user 1304611131
2025-07-30 02:21:03,333 - config - INFO - Processing message from user 1304611131: Yeasterday...
2025-07-30 02:21:08,349 - config - INFO - Sent response to user 1304611131
2025-07-30 02:22:09,958 - config - INFO - Processing message from user 1304611131: Who is akbar...
2025-07-30 02:22:10,819 - ai_processor - INFO - Detected general knowledge question, not escalating: Who is akbar...
2025-07-30 02:22:16,879 - config - INFO - Sent response to user 1304611131
