{"test_user": {"messages": [{"timestamp": "2025-07-30T01:31:33.047307", "message": "user", "is_user": "Hello", "metadata": {}}, {"timestamp": "2025-07-30T01:31:41.593154", "message": "user", "is_user": "Hello", "metadata": {}}, {"timestamp": "2025-07-30T01:31:41.593540", "message": "assistant", "is_user": "Hi there!", "metadata": {}}], "first_interaction": "2025-07-30T01:31:33.047245", "last_interaction": "2025-07-30T01:31:41.593537", "user_info": {}, "context": {}}, "1304611131": {"messages": [{"timestamp": "2025-07-30T01:32:00.846219", "message": "/start", "is_user": true, "metadata": {}}, {"timestamp": "2025-07-30T01:32:00.847877", "message": "Hello <PERSON><PERSON><PERSON>! 👋 Welcome to our support channel. How can I assist you today?", "is_user": false, "metadata": {}}, {"timestamp": "2025-07-30T01:32:33.267677", "message": "/start", "is_user": true, "metadata": {}}, {"timestamp": "2025-07-30T01:32:33.269201", "message": "Hi <PERSON><PERSON><PERSON>! I'm here to help with any questions or issues you might have. What can I do for you?", "is_user": false, "metadata": {}}, {"timestamp": "2025-07-30T01:32:42.385398", "message": "hi baby", "is_user": true, "metadata": {}}, {"timestamp": "2025-07-30T01:32:45.805978", "message": "Thank you for your message! I'm here to help you with your inquiry.\n\nI'm currently experiencing some technical difficulties, but I'd be happy to assist you. Could you please provide more details about what you need help with?\n\nIf you need immediate assistance, please contact our human support team at @your_support_username.\n\nI apologize for any inconvenience and appreciate your patience! 🙏", "is_user": false, "metadata": {"intent": {"intent": "question", "urgency": "medium", "requires_human": false, "topics": ["general"], "sentiment": "neutral"}, "escalated": false}}, {"timestamp": "2025-07-30T01:34:24.163322", "message": "/start", "is_user": true, "metadata": {}}, {"timestamp": "2025-07-30T01:34:24.165422", "message": "Welcome <PERSON><PERSON><PERSON>! 🌟 I'm your customer support assistant. Feel free to ask me anything!", "is_user": false, "metadata": {}}, {"timestamp": "2025-07-30T01:46:14.450508", "message": "/start", "is_user": true, "metadata": {}}, {"timestamp": "2025-07-30T01:46:14.452265", "message": "Hello <PERSON><PERSON><PERSON>! 👋 Welcome to our support channel. How can I assist you today?", "is_user": false, "metadata": {}}, {"timestamp": "2025-07-30T01:46:22.054645", "message": "hi", "is_user": true, "metadata": {}}, {"timestamp": "2025-07-30T01:46:25.430285", "message": "Thank you for your message! I'm here to help you with your inquiry.\n\nI'm currently experiencing some technical difficulties, but I'd be happy to assist you. Could you please provide more details about what you need help with?\n\nIf you need immediate assistance, please contact our human support team at @your_support_username.\n\nI apologize for any inconvenience and appreciate your patience! 🙏", "is_user": false, "metadata": {"intent": {"intent": "question", "urgency": "medium", "requires_human": false, "topics": ["general"], "sentiment": "neutral"}, "escalated": false}}, {"timestamp": "2025-07-30T01:52:11.012801", "message": "/start", "is_user": true, "metadata": {}}, {"timestamp": "2025-07-30T01:52:11.014543", "message": "Hi <PERSON><PERSON><PERSON>! I'm here to help with any questions or issues you might have. What can I do for you?", "is_user": false, "metadata": {}}, {"timestamp": "2025-07-30T01:52:15.990652", "message": "hii", "is_user": true, "metadata": {}}, {"timestamp": "2025-07-30T01:52:19.364849", "message": "Thank you for your message! I'm here to help you with your inquiry.\n\nI'm currently experiencing some technical difficulties, but I'd be happy to assist you. Could you please provide more details about what you need help with?\n\nIf you need immediate assistance, please contact our human support team at @your_support_username.\n\nI apologize for any inconvenience and appreciate your patience! 🙏", "is_user": false, "metadata": {"intent": {"intent": "question", "urgency": "medium", "requires_human": false, "topics": ["general"], "sentiment": "neutral"}, "escalated": false}}, {"timestamp": "2025-07-30T01:53:23.117750", "message": "vjhv", "is_user": true, "metadata": {}}, {"timestamp": "2025-07-30T01:53:27.040246", "message": "Thank you for your message! I'm here to help you with your inquiry.\n\nI'm currently experiencing some technical difficulties, but I'd be happy to assist you. Could you please provide more details about what you need help with?\n\nIf you need immediate assistance, please contact our human support team at @your_support_username.\n\nI apologize for any inconvenience and appreciate your patience! 🙏", "is_user": false, "metadata": {"intent": {"intent": "question", "urgency": "medium", "requires_human": false, "topics": ["general"], "sentiment": "neutral"}, "escalated": false}}, {"timestamp": "2025-07-30T01:54:52.280591", "message": "hgkjh", "is_user": true, "metadata": {}}, {"timestamp": "2025-07-30T01:54:56.378938", "message": "Thank you for your message! I'm here to help you with your inquiry.\n\nI'm currently experiencing some technical difficulties, but I'd be happy to assist you. Could you please provide more details about what you need help with?\n\nIf you need immediate assistance, please contact our human support team at @your_support_username.\n\nI apologize for any inconvenience and appreciate your patience! 🙏", "is_user": false, "metadata": {"intent": {"intent": "question", "urgency": "medium", "requires_human": false, "topics": ["general"], "sentiment": "neutral"}, "escalated": false}}], "first_interaction": "2025-07-30T01:31:58.941270", "last_interaction": "2025-07-30T01:54:56.378921", "user_info": {"username": "Anku_yadv", "first_name": "Ankit", "last_name": "<PERSON><PERSON><PERSON>", "language_code": "en"}, "context": {}}}