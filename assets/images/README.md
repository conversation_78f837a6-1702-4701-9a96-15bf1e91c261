# Profile Photo Setup for Ankit Yadav's Customer Support Bot

## Photo Requirements

To enable the personalized welcome message with your photo, please add your profile photo to this directory.

### File Requirements:
- **Filename**: `ankit_profile.jpg` (exactly this name)
- **Format**: JPG/JPEG (recommended) or PNG
- **Size**: Recommended 800x800 pixels or similar square aspect ratio
- **File size**: Under 10MB (Telegram limit)
- **Quality**: High resolution for professional appearance

### Recommended Photo Characteristics:
- **Professional headshot** or business photo
- **Clear, well-lit** image
- **Neutral or professional background**
- **Friendly, approachable expression**
- **Business attire** (optional but recommended)

## How to Add Your Photo:

1. **Prepare your photo** according to the requirements above
2. **Rename the file** to exactly `ankit_profile.jpg`
3. **Place the file** in this directory: `assets/images/ankit_profile.jpg`
4. **Restart the bot** to enable photo functionality

## Current Status:
- ❌ **Photo not found** - The bot will use text-only welcome messages
- ✅ **Photo ready** - The bot will send welcome messages with your photo

## Alternative Options:

If you prefer not to use a photo, the bot will automatically fall back to:
- **Rich text welcome** with inline keyboard buttons
- **Professional branding** with your name
- **Interactive buttons** for user engagement

## Testing:

After adding your photo:
1. Stop the current bot (Ctrl+C)
2. Restart with `python3 run_bot.py`
3. Test with a new user or clear your user data
4. Send `/start` command to see the welcome with photo

## Photo Privacy:

- The photo is stored locally on your server
- Only sent to users who interact with your bot
- Not shared with external services
- You can remove or replace it anytime
