#!/usr/bin/env python3
"""
Entry point for running the Telegram Customer Support Bot.
"""
import asyncio
import signal
import sys
import logging
from telegram_bot import main

logger = logging.getLogger(__name__)

async def run_with_shutdown():
    """Run the bot with proper shutdown handling."""
    # Create shutdown event
    shutdown_event = asyncio.Event()

    # Set up signal handlers using asyncio
    loop = asyncio.get_running_loop()

    def signal_handler():
        logger.info("Received shutdown signal, stopping bot...")
        shutdown_event.set()

    # Register signal handlers
    for sig in (signal.SIGTERM, signal.SIGINT):
        loop.add_signal_handler(sig, signal_handler)

    # Create the main task
    main_task = asyncio.create_task(main(shutdown_event))

    # Wait for either the main task to complete or shutdown signal
    try:
        await main_task
        logger.info("Bot completed successfully")
    except asyncio.CancelledError:
        logger.info("Bot operation was cancelled")
    except Exception as e:
        logger.error(f"Bot crashed: {e}")
        raise
    finally:
        # Clean up signal handlers
        for sig in (signal.SIGTERM, signal.SIGINT):
            loop.remove_signal_handler(sig)

if __name__ == "__main__":
    try:
        asyncio.run(run_with_shutdown())
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot crashed: {e}")
        sys.exit(1)
