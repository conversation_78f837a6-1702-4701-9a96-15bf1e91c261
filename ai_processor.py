"""
AI-powered message processing using Google Gemini API.
"""
import google.generativeai as genai
import asyncio
import logging
from typing import Dict, List, Optional
from datetime import datetime
from config import Config

logger = logging.getLogger(__name__)

class AIProcessor:
    """Handles AI-powered response generation and intent recognition."""
    
    def __init__(self):
        self.model = None
        self.setup_gemini()
        
    def setup_gemini(self):
        """Initialize Gemini AI model."""
        try:
            genai.configure(api_key=Config.GEMINI_API_KEY)
            # Use the current supported model instead of deprecated gemini-pro
            self.model = genai.GenerativeModel('gemini-2.0-flash')
            logger.info("Gemini AI model initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Gemini AI: {e}")
            raise
    
    async def generate_response(self, user_message: str, conversation_context: str = "", user_info: Dict = None) -> str:
        """Generate an AI response to user message."""
        try:
            # Build the prompt with context
            prompt = self._build_prompt(user_message, conversation_context, user_info)
            
            # Generate response using Gemini
            response = await asyncio.to_thread(self.model.generate_content, prompt)
            
            if response and response.text:
                return response.text.strip()
            else:
                return self._get_fallback_response()
                
        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            return self._get_fallback_response()
    
    def _build_prompt(self, user_message: str, conversation_context: str = "", user_info: Dict = None) -> str:
        """Build a comprehensive prompt for the AI model."""

        system_prompt = f"""You are {Config.BOT_NAME}, a knowledgeable AI assistant that can help with both general questions and customer support. You have access to vast knowledge and can answer factual, educational, and general knowledge questions directly.

WHAT YOU CAN ANSWER DIRECTLY:
- General knowledge questions (geography, science, history, facts)
- Educational queries ("How many cities in India?", "Distance between countries", etc.)
- Definitions and explanations
- Factual information about places, people, concepts
- Mathematical calculations
- General "how-to" questions

GUIDELINES FOR DIRECT ANSWERS:
1. Answer factual and general knowledge questions confidently and accurately
2. Provide clear, informative responses with relevant details
3. Be helpful and educational
4. Use a friendly, conversational tone
5. If you're not certain about specific facts, acknowledge uncertainty but still provide helpful information

ESCALATION SCENARIOS (refer to human support at {Config.SUPPORT_CONTACT}):
- Account-specific problems requiring access to user data
- Billing or payment issues with this service
- Technical problems with this specific service/product
- Complaints about this service
- Requests for refunds or account changes for this service
- Service-related disputes

CONVERSATION CONTEXT:
{conversation_context if conversation_context else "This is a new conversation."}

USER MESSAGE: {user_message}

Please provide a helpful response. If this is a general knowledge or factual question, answer it directly and thoroughly:"""

        return system_prompt
    
    async def analyze_intent(self, user_message: str) -> Dict[str, any]:
        """Analyze user message to determine intent and extract key information."""
        try:
            intent_prompt = f"""Analyze the following user message and categorize it carefully:

INTENT CATEGORIES:
- general_knowledge: Factual questions, geography, science, history, definitions, "how many", "what is", "distance between", etc.
- greeting: Hello, hi, good morning, etc.
- customer_support: Account issues, billing problems, service complaints, technical problems with the service
- complaint: Negative feedback about service, dissatisfaction, anger
- billing: Payment issues, charges, refunds, subscription problems
- technical_issue: Problems with the actual service/product functionality
- other: Anything else

HUMAN SUPPORT DECISION:
- Answer "no" for: general knowledge, greetings, factual questions, educational queries, geography, science, definitions
- Answer "yes" for: billing issues, account problems, service complaints, refund requests, technical service issues

User message: "{user_message}"

Analyze this message:
1. Primary intent (use categories above)
2. Urgency level (low, medium, high)
3. Requires human support (yes/no) - follow the guidelines above strictly
4. Key topics mentioned
5. Sentiment (positive, neutral, negative)

Respond in this exact format:
Intent: [intent]
Urgency: [urgency]
Human Support: [yes/no]
Topics: [comma-separated topics]
Sentiment: [sentiment]"""

            response = await asyncio.to_thread(self.model.generate_content, intent_prompt)
            
            if response and response.text:
                return self._parse_intent_response(response.text)
            else:
                return self._get_default_intent()
                
        except Exception as e:
            logger.error(f"Error analyzing intent: {e}")
            return self._get_default_intent()
    
    def _parse_intent_response(self, response_text: str) -> Dict[str, any]:
        """Parse the intent analysis response."""
        try:
            lines = response_text.strip().split('\n')
            intent_data = {}
            
            for line in lines:
                if ':' in line:
                    key, value = line.split(':', 1)
                    key = key.strip().lower().replace(' ', '_')
                    value = value.strip()
                    
                    if key == 'intent':
                        intent_data['intent'] = value.lower()
                    elif key == 'urgency':
                        intent_data['urgency'] = value.lower()
                    elif key == 'human_support':
                        intent_data['requires_human'] = value.lower() == 'yes'
                    elif key == 'topics':
                        intent_data['topics'] = [topic.strip() for topic in value.split(',')]
                    elif key == 'sentiment':
                        intent_data['sentiment'] = value.lower()
            
            return intent_data
            
        except Exception as e:
            logger.error(f"Error parsing intent response: {e}")
            return self._get_default_intent()
    
    def _get_default_intent(self) -> Dict[str, any]:
        """Return default intent analysis."""
        return {
            'intent': 'question',
            'urgency': 'medium',
            'requires_human': False,
            'topics': ['general'],
            'sentiment': 'neutral'
        }
    
    def _get_fallback_response(self) -> str:
        """Return a fallback response when AI fails."""
        return f"""Thank you for your message! I'm here to help you with your inquiry.

I'm currently experiencing some technical difficulties, but I'd be happy to assist you. Could you please provide more details about what you need help with?

If you need immediate assistance, please contact our human support team at {Config.SUPPORT_CONTACT}.

I apologize for any inconvenience and appreciate your patience! 🙏"""

    async def generate_greeting_response(self, user_name: str = None) -> str:
        """Generate a personalized greeting response."""
        name_part = f" {user_name}" if user_name else ""
        
        greetings = [
            f"Hello{name_part}! 👋 Welcome to our support channel. How can I assist you today?",
            f"Hi{name_part}! I'm here to help with any questions or issues you might have. What can I do for you?",
            f"Welcome{name_part}! 🌟 I'm your customer support assistant. Feel free to ask me anything!"
        ]
        
        # Use AI to generate a more personalized greeting if possible
        try:
            prompt = f"Generate a friendly, professional greeting for a customer support bot. The user's name is {user_name if user_name else 'not provided'}. Keep it brief and welcoming."
            response = await asyncio.to_thread(self.model.generate_content, prompt)
            
            if response and response.text:
                return response.text.strip()
        except Exception as e:
            logger.error(f"Error generating AI greeting: {e}")
        
        # Fallback to predefined greetings
        import random
        return random.choice(greetings)
    
    async def should_escalate_to_human(self, intent_data: Dict, user_message: str) -> bool:
        """Determine if the conversation should be escalated to human support."""

        # First, check for high-priority escalation keywords that should always escalate
        high_priority_escalation = [
            'speak to manager', 'need manager', 'want manager', 'talk to manager',
            'speak to supervisor', 'need supervisor', 'want supervisor',
            'human support', 'human agent', 'real person'
        ]

        message_lower = user_message.lower()

        # Check for high-priority escalation first
        if any(keyword in message_lower for keyword in high_priority_escalation):
            logger.info(f"Detected high-priority escalation request: {user_message[:50]}...")
            return True

        # Then check if this is clearly a general knowledge question that should NOT be escalated
        general_knowledge_indicators = [
            'how many', 'what is', 'what are', 'distance between', 'distance from',
            'capital of', 'population of', 'where is', 'when was', 'who is',
            'define', 'explain', 'tell me about', 'information about'
        ]

        # Special patterns for geography questions
        geography_patterns = [
            'from ' + r'\w+' + ' to ',  # "from X to Y"
            'between ' + r'\w+' + ' and ',  # "between X and Y"
        ]

        # Check for general knowledge patterns
        is_general_knowledge = False

        # Check simple indicators
        if any(indicator in message_lower for indicator in general_knowledge_indicators):
            is_general_knowledge = True

        # Check if it starts with "from" and looks like a geography question
        if message_lower.startswith('from ') and len(message_lower.split()) <= 3:
            is_general_knowledge = True

        if is_general_knowledge:
            logger.info(f"Detected general knowledge question, not escalating: {user_message[:50]}...")
            return False

        # Check intent type - don't escalate general knowledge or greetings
        intent = intent_data.get('intent', '').lower()
        if intent in ['general_knowledge', 'greeting', 'question']:
            return False

        # Check intent analysis for explicit human support requirement
        if intent_data.get('requires_human', False):
            # Double-check: if intent is general_knowledge but requires_human is True, prefer not escalating
            if intent == 'general_knowledge':
                return False
            return True

        # Check urgency and sentiment combination for customer support issues
        if (intent_data.get('urgency') == 'high' and
            intent_data.get('sentiment') == 'negative' and
            intent in ['customer_support', 'complaint', 'billing', 'technical_issue']):
            return True

        # Check for specific customer support keywords that require human intervention
        escalation_keywords = [
            'refund', 'cancel subscription', 'billing issue', 'payment problem',
            'charge error', 'account locked', 'account suspended', 'can\'t login',
            'angry', 'frustrated', 'complaint', 'manager', 'supervisor',
            'legal', 'lawsuit', 'dispute', 'unauthorized charge', 'fraud',
            'delete account', 'close account', 'technical support', 'bug report'
        ]

        # Only escalate if we find customer support keywords AND it's not a general knowledge question
        if any(keyword in message_lower for keyword in escalation_keywords):
            # Additional check: make sure it's not just mentioning these words in a general context
            general_context_phrases = [
                'what is a refund', 'how does billing work', 'what is a complaint',
                'define', 'explain', 'tell me about', 'information about'
            ]

            if not any(phrase in message_lower for phrase in general_context_phrases):
                return True

        return False
